header:
  license:
    spdx-id: Apache-2.0
    content: |
      Licensed to the Apache Software Foundation (ASF) under one or more
      contributor license agreements.  See the NOTICE file distributed with
      this work for additional information regarding copyright ownership.
      The ASF licenses this file to You under the Apache License, Version 2.0
      (the "License"); you may not use this file except in compliance with
      the License.  You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

      Unless required by applicable law or agreed to in writing, software
      distributed under the License is distributed on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
      See the License for the specific language governing permissions and
      limitations under the License.

  paths-ignore:
    - '**/*.versionsBackup'
    - '**/.idea/'
    - '**/*.iml'
    - '**/.settings/*'
    - '**/.classpath'
    - '**/.project'
    - '**/target/**'
    - '**/generated/**'
    - '**/*.log'
    - '**/codestyle/*'
    - '**/resources/META-INF/**'
    - '**/resources/mockito-extensions/**'
    - '**/*.proto'
    - '**/*.cache'
    - '**/*.txt'
    - '**/*.load'
    - '**/*.flex'
    - '**/*.fc'
    - '**/*.javascript'
    - '**/*.properties'
    - '**/*.thrift'
    - '**/*.sh'
    - '**/*.bat'
    - '**/*.md'
    - '**/*.svg'
    - '**/*.png'
    - '**/*.json'
    - '**/*.conf'
    - '**/*.ftl'
    - '**/*.tpl'
    - '**/*.factories'
    - '**/*.handlers'
    - '**/*.schemas'
    - '**/*.nojekyll'
    - '.git/'
    - '.github/**'
    - '**/.gitignore'
    - '**/.helmignore'
    - '.repository/'
    - 'compiler/**'
    - '.gitmodules'
    - '**/.mvn/**'
    - 'mvnw'
    - 'mvnw.cmd'
    - 'LICENSE'
    - 'NOTICE'
    - 'CNAME'
    - 'Jenkinsfile'
    - '**/vendor/**'
    - '**/EmbeddedZooKeeper.java'
    - 'test/scripts/case-versions.conf.*'
    - '**/*.md'
    - '**/*.MD'
    - '**/src/main/resources/certs/**'
    - '**/dubbo-samples-tengine/dubbo-samples-tengine-provider/src/main/resources/docker/sources.list'
    - '**/.run/**'
    - '**/*.js'
    - '**/Dockerfile*'
    - '**/*.xml'
    - '**/*.properties'
    - '**/*.yml'
    - '**/*.yaml'
    - 'online_bontique_demo/frontend/src/main/resources/static/styles/bootstrap.min.css'


  comment: on-failure

  license-location-threshold: 130

dependency:
  files:
    - pom.xml
