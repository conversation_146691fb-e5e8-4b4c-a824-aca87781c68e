#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

spring:
  ai:

    # 向量数据库配置
    vectorstore:
      analytic:
        enabled: false
        dbInstanceId: gp-bp1238pvyz0g78u2m
        regionId: cn-hangzhou
        managerAccount: ${ADB_MANAGER_ACCOUNT}
        managerAccountPassword: ${ADB_MANAGER_ACCOUNT_PASSWORD}
        namespace: saa
        namespacePassword: saa@2025
        collectName: saa
        access-key-id: ${ADB_ACCESS_KEY_ID}
        access-key-secret: ${ADB_ACCESS_KEY_SECRET}

    dashscope:
      api-key: ${AI_DASHSCOPE_API_KEY}
    # 使用自定义增强的 mcp server config 替换
    # mcp:
    #   client:
    #     stdio:
    #       servers-configuration: classpath:/mcp-servers-config.json

    mcp:
      client:
        toolcallback:
          enabled: true

    alibaba:
     playground:

       # IQS 数据搜索服务配置
       iqs:
         search:
           api-key: ${IQS_SEARCH_API_KEY}

       # 百炼知识库
       bailian:
         enable: true
         index-name: "saa-playground-2"

       # 需要配置相关 tools 的信息
       tool-calling:
         baidu:
           translate:
             ak: ${BAIDU_TRANSLATE_APP_ID:input-your-baidu-app-id}
             sk: ${BAIDU_TRANSLATE_SECRET_KEY:input-your-baidu-app-key}
           map:
             ak: ${BAIDU_MAP_API_KEY:input-your-baidu-map-key}

    # openai api 接入通义模型
    openai:
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      api-key: ${AI_DASHSCOPE_API_KEY}
      chat:
        options:
          model: qwen-max

    # 注意：观测数据可能存在敏感信息！
    chat:
      client:
        observations:
          # 记录调用输入
          include-input: true
      observations:
        # 记录 LLMs 输出
        include-completion: true
        # 记录 prompt
        include-prompt: true
        include-error-logging: true

  datasource:
    url: jdbc:sqlite:db/saa.db
    driver-class-name: org.sqlite.JDBC

  jpa:
    show-sql: true
    database-platform: org.hibernate.community.dialect.SQLiteDialect

    hibernate:
      ddl-auto: create-drop

    properties:
      hibernate:
        boot:
          allow_jdbc_metadata_access: false

# enabled debug log out.
logging:
  level:
    org.springframework.ai: debug
    com.alibaba.dashscope.api: debug
    com.alibaba.cloud.ai.application: debug

# 部署时不配置，使用默认配置
management:
  endpoints:
    web:
      exposure:
        # 开放所有外部端点
        include: "*"
  endpoint:
    health:
      # 应用健康状态检查，携带详细新学年
      show-details: always
  tracing:
    sampling:
      # trace 采样信息，记录每个请求
      probability: 1.0
