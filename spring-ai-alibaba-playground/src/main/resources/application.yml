#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

server:
  port: 8080

spring:
  application:
    name: spring-ai-alibaba-playground

  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB

  profiles:
    active: dev

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.alibaba.cloud.ai.application

knife4j:
  #是否启用增强设置
  enable: true
  #开启生产环境屏蔽
  production: true
  #是否启用登录认证
  # basic:
  #   enable: true
  #  username: admin
  #  password: 123456
  setting:
    language: zh_cn
    enable-version: true
    enable-swagger-models: true
