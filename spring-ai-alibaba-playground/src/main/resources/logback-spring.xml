<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<configuration scan="true">
    <springProperty scope="context" name="application_name" source="spring.application.name" defaultValue="server"/>
    <!-- Output logs to ConsoleAppender -->
    <appender name="ConsoleAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!--<pattern>%d %p (%file:%line\)- %m%n</pattern>-->
            <!-- Format output: %d for date, %thread for thread name, %-5level for level with 5 character width, %msg for log message, %n for new line -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="SystemOutFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- Logger rolling policy, by date and by size -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- Archived log file path. %d{yyyy-MM-dd} specifies date format, %i specifies index -->
            <fileNamePattern>logs/${application_name}-%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- Log retention duration -->
            <maxHistory>7</maxHistory>
            <!-- Maximum size of log retention -->
            <totalSizeCap>5GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <!-- Besides logging by day, log files cannot exceed 200M, if exceeded, log files will start from index 0 -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>50MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!-- Append mode for logging -->
        <append>true</append>
        <!-- Log file format -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger Line:%-3L - %msg%n</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <appender name="ErrOutFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/${application_name}-%d{yyyy-MM-dd}-error.%i.log.zip</fileNamePattern>
            <!-- Log retention duration -->
            <maxHistory>7</maxHistory>
            <!-- Maximum size of log retention -->
            <totalSizeCap>5GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>50MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!-- Append mode for logging -->
        <append>true</append>
        <!-- Log file format -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger Line:%-3L - %msg%n</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!-- This log file records error and above levels -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- Settings for this logger: for example, all output logs under the org.springframework package must be at level info or above to be output! -->
    <!-- This can avoid outputting many common debug information of the spring framework! -->
    <logger name="org.springframework" level="info"/>
    <logger name="org.json" level="error"/>
    <logger name="io.netty" level="info"/>
    <logger name="org.slf4j" level="info"/>
    <logger name="ch.qos.logback" level="warn"/>
    <logger name="org.apache.kafka.clients" level="info"/>
    <logger name="org.hibernate" level="info"/>
    <logger name="org.apache.http" level="info"/>
    <logger name="com.zaxxer" level="info"/>
    <logger name="springfox" level="info"/>
    <logger name="org.mongodb" level="warn"/>
    <logger name="io.greptime" level="warn"/>

    <!-- Production environment configuration -->
    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="SystemOutFileAppender"/>
            <appender-ref ref="ErrOutFileAppender"/>
        </root>
        <!-- North log -->
        <Logger name="com.obs.services.AbstractClient" level="OFF"
                additivity="false">
        </Logger>

        <!-- South log -->
        <Logger name="com.obs.services.internal.RestStorageService" level="OFF"
                additivity="false">
        </Logger>
        <!-- Access log -->
        <Logger name="com.obs.log.AccessLogger" level="OFF"
                additivity="false">
        </Logger>
    </springProfile>

    <!-- Development environment configuration -->
    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="ConsoleAppender"/>
            <appender-ref ref="SystemOutFileAppender"/>
            <appender-ref ref="ErrOutFileAppender"/>
        </root>
    </springProfile>

        <springProfile name="docker">
        <root level="INFO">
            <appender-ref ref="ConsoleAppender"/>
            <appender-ref ref="SystemOutFileAppender"/>
            <appender-ref ref="ErrOutFileAppender"/>
        </root>
    </springProfile>

    <!-- Development environment configuration -->
    <springProfile name="mysql">
        <root level="INFO">
            <appender-ref ref="ConsoleAppender"/>
            <appender-ref ref="SystemOutFileAppender"/>
            <appender-ref ref="ErrOutFileAppender"/>
        </root>
    </springProfile>

    <!-- Development environment configuration -->
    <springProfile name="pg">
        <root level="INFO">
            <appender-ref ref="ConsoleAppender"/>
            <appender-ref ref="SystemOutFileAppender"/>
            <appender-ref ref="ErrOutFileAppender"/>
        </root>
    </springProfile>

</configuration>
