/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.cloud.ai.application.controller;

import com.alibaba.cloud.ai.application.annotation.UserIp;
import com.alibaba.cloud.ai.application.entity.result.Result;
import com.alibaba.cloud.ai.application.service.SAAAudioService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 浏览器的语音 API 不好交互，此功能仅作为演示，切勿直接用于生产环境。
 *
 * <AUTHOR>
 * <AUTHOR> href="mailto:<EMAIL>">yuluo</a>
 */

@RestController
@Tag(name = "Audio APIs")
@RequestMapping("/api/v1/")
public class SAAAudioController {

	private final SAAAudioService audioService;

	public SAAAudioController(SAAAudioService audioService) {
		this.audioService = audioService;
	}

	/**
	 * used to convert audio to text output
	 */
	@UserIp
	@PostMapping("/audio2text")
	@Operation(summary = "DashScope Audio Transcription")
	public Result<String> audioToText(
			@Validated @RequestParam("audio") MultipartFile audio
	) throws IOException {

		if (audio.isEmpty()) {
			return Result.failed("No audio file provided");
		}

		return Result.success(audioService.audio2text(audio));
	}

	/**
	 * used to convert text into speech output
	 */
	@UserIp
	@GetMapping("/text2audio")
	@Operation(summary = "DashScope Speech Synthesis")
	public Result<byte[]> textToAudio(
			@Validated @RequestParam("prompt") String prompt
	) {

		return Result.success("not implemented yet".getBytes());

		// byte[] audioData = audioService.text2audio(prompt);

		// test to verify that the audio data is empty
		// try (FileOutputStream fos = new FileOutputStream("tmp/audio/test-audio.wav")) {
		// 	fos.write(audioData);
		// } catch (IOException e) {
		// 	return Result.failed("Test save audio file: " + e.getMessage());
		// }
		//
		// return Result.success(audioData);
	}

}
