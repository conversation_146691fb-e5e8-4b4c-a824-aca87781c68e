diff --git a/es/useXAgent/index.js b/es/useXAgent/index.js
index 0e827decc3c1707548ec3a90a8f9bf5dc6f9c6cf..293b15bc3548cbba1463cd37d0c285f437df2825 100644
--- a/es/useXAgent/index.js
+++ b/es/useXAgent/index.js
@@ -55,12 +55,12 @@ export default function useXAgent(config) {
     request,
     ...restConfig
   } = config;
-  return React.useMemo(() => [new XAgent({
+  return [new XAgent({
     request: request || XRequest({
       baseURL: restConfig.baseURL,
       model: restConfig.model,
       dangerouslyApiKey: restConfig.dangerouslyApiKey
     }).create,
     ...restConfig
-  })], []);
+  })];
 }
