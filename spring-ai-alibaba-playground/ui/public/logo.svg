<svg xmlns="http://www.w3.org/2000/svg" width="500" height="300" viewBox="0 0 500 300">
  <defs>
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4CAF50"/>
      <stop offset="100%" stop-color="#0D47A1"/>
    </linearGradient>
    
    <linearGradient id="leafGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2ECC71"/>
      <stop offset="100%" stop-color="#27AE60"/>
    </linearGradient>
  </defs>

  <rect width="500" height="300" fill="transparent"/>

  <g transform="translate(250,150) scale(0.6)">
    <g transform="translate(-100,0)">
      <path 
        d="M0,-60 
           C-40,-40 -40,0 0,20 
           C40,0 40,-40 0,-60" 
        fill="url(#leafGradient)"
        opacity="0.7"
        stroke="#2ECC71"
        stroke-width="2"
      />
      
      <g stroke="#FFFFFF" stroke-width="2" opacity="0.5">
        <line x1="0" y1="-50" x2="0" y2="10" />
        <line x1="-20" y1="-30" x2="20" y2="-10" />
      </g>
    </g>

    <g transform="translate(0,0)" stroke="url(#aiGradient)" stroke-width="4">
      <line 
        x1="-80" 
        y1="0" 
        x2="80" 
        y2="0" 
        stroke="url(#aiGradient)"
      />
      
      <g transform="translate(0,-30)">
        <text 
          x="0" 
          y="0" 
          text-anchor="middle" 
          font-family="Arial" 
          font-size="40" 
          fill="url(#aiGradient)"
          font-weight="bold"
        >
          AI
        </text>
        
        <g fill="url(#aiGradient)">
          <path 
            d="M0,20 
               L-20,50 
               L20,50 
               Z" 
            opacity="0.3"
          />
          <line 
            x1="0" 
            y1="20" 
            x2="0" 
            y2="80" 
            stroke="url(#aiGradient)" 
            stroke-width="4"
          />
        </g>
        
        <g stroke="url(#aiGradient)" stroke-width="2" opacity="0.5">
          <line x1="-20" y1="10" x2="0" y2="20"/>
          <line x1="0" y1="10" x2="0" y2="20"/>
          <line x1="20" y1="10" x2="0" y2="20"/>
        </g>
      </g>
    </g>

    <g transform="translate(100,0)">
      <path 
        d="M0,-60 
           C40,-40 40,0 0,20 
           C-40,0 -40,-40 0,-60" 
        fill="url(#leafGradient)"
        opacity="0.7"
        stroke="#2ECC71"
        stroke-width="2"
      />

      <g stroke="#FFFFFF" stroke-width="2" opacity="0.5">
        <line x1="0" y1="-50" x2="0" y2="10" />
        <line x1="-20" y1="-30" x2="20" y2="-10" />
      </g>
      
      <g>
        <g fill="url(#aiGradient)" opacity="0.6">
          <circle cx="0" cy="-30" r="4"/>
          <circle cx="-20" cy="-10" r="3"/>
          <circle cx="20" cy="-10" r="3"/>
          <circle cx="0" cy="10" r="3"/>
        </g>

        <g stroke="url(#aiGradient)" stroke-width="1" opacity="0.5">
          <line x1="0" y1="-30" x2="-20" y2="-10"/>
          <line x1="0" y1="-30" x2="20" y2="-10"/>
          <line x1="-20" y1="-10" x2="0" y2="10"/>
          <line x1="20" y1="-10" x2="0" y2="10"/>
          <line x1="0" y1="-30" x2="0" y2="10"/>
        </g>
      </g>
    </g>
  </g>

  <text 
    x="250" 
    y="220" 
    text-anchor="middle" 
    font-family="Arial" 
    font-size="24" 
    fill="url(#aiGradient)"
    font-weight="bold"
    opacity="0.7"
  >
    Spring AI Alibaba Playground
  </text>
</svg>