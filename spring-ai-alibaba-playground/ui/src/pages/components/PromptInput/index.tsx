// TODO: 获取能够有更通用的输入框...或许，也可以期待 antx 侧后续的更新
// TODO: 需要有可拔插的架构...
// import { Sender } from "@ant-design/x";
// import React from "react";

// interface PromptInputProps {
//   actionButtons: React.ReactNode;
//   content: string;
//   senderHeader: React.ReactNode;
//   onSubmit: (content: string) => void;
//   onChange: (content: string) => void;
// }

// const PromptInput = ({ actionButtons }) => {
//   return (
//     <div>
//       {actionButtons}
//       <div style={{ position: "relative" }}>
//         <Sender
//           value={content}
//           header={senderHeader}
//           onSubmit={onSubmit}
//           allowSpeech
//           onChange={setContent}
//           prefix={attachmentsNode}
//           loading={agent.isRequesting()}
//           className={styles.sender}
//           placeholder={"您可以问我任何问题..."}
//         />
//       </div>
//     </div>
//   );
// };

// export default PromptInput;
