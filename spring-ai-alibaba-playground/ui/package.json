{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && npm run mv-bundler", "mv-bundler": "mkdir -p ../src/main/resources/static && cp -r dist/* ../src/main/resources/static/", "lint": "eslint .", "preview": "vite preview", "start:backend": "cd .. && mvn spring-boot:run -Dspring-boot.run.profiles=dev -Dskip.npm", "start:all": "concurrently \"npm run start:backend\" \"npm run dev\""}, "dependencies": {"@ant-design/icons": "^5.5.2", "@ant-design/x": "1.0.4", "@atom-universe/use-web-worker": "^2.1.0", "@types/react-syntax-highlighter": "^15.5.13", "antd": "^5.23.0", "antd-style": "^3.7.1", "framer-motion": "^12.6.3", "jotai": "^2.12.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.0.0", "react-masonry-css": "^1.0.16", "react-router-dom": "^7.4.1", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.4", "concurrently": "^9.1.2", "eslint": "^9.17.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "typescript": "^5.7.3", "vite": "^6.0.5"}}