### Ollama 文件翻译 (需要上传文件) - 使用 postman 或类似工具测试

### DashScope 翻译测试

### 基础翻译 - 中文到英文
GET http://localhost:8080/api/dashscope/translate/simple?text=你好，世界！&sourceLanguage=中文&targetLanguage=英文

### 基础翻译 - 英文到中文
GET http://localhost:8080/api/dashscope/translate/simple?text=Hello, World!&sourceLanguage=英文&targetLanguage=中文

### 流式翻译 - 中文到英文
GET http://localhost:8080/api/dashscope/translate/stream?text=人工智能正在改变我们的生活方式。&sourceLanguage=中文&targetLanguage=英文

### 自定义翻译 - 中文到日文
GET http://localhost:8080/api/dashscope/translate/custom?text=春天来了，花儿开了。&sourceLanguage=中文&targetLanguage=日文 
