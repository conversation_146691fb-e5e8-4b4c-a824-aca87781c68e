<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.alibaba.cloud.ai</groupId>
		<artifactId>spring-ai-alibaba-examples</artifactId>
		<version>${revision}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<groupId>org.example</groupId>
	<artifactId>spring-ai-alibaba-usecase-example</artifactId>
	<packaging>pom</packaging>

	<properties>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<modules>
		<module>spring-ai-alibaba-scene-example</module>
		<module>spring-ai-alibaba-sql-example</module>
		<module>spring-ai-alibaba-text-classification-example</module>
		<module>spring-ai-alibaba-text-summarizer-example</module>
		<module>spring-ai-alibaba-translate-example</module>
		<module>spring-ai-alibaba-classification-grading-example</module>
	</modules>

</project>
