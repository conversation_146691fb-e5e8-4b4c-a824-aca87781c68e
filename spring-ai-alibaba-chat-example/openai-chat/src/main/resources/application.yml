server:
  port: 10003

spring:
  application:
    name: spring-ai-alibaba-openai-chat-model-example
  ai:
    openai:
      api-key: ${OPENAI_API_KEY}
      base-url: https://api.openai-hk.com
      
#  兼容其他OpenAI格式的大模型配置示例
#  以下为字节火山引擎·方舟大模型(Ark LLM)配置示例
#  ai:
#    openai:
#      # API密钥配置
#      api-key: ${OPENAI_API_KEY}
#      # 方舟大模型API地址
#      base-url: https://ark.cn-beijing.volces.com/api/
#      chat:
#        options:
#          # 模型ID,需要替换为实际的接入点ID
#          model: ${OPENAI_MODEL_ID}
#        # Chat接口路径,与OpenAI接口保持一致
#        completions-path: /v3/chat/completions
