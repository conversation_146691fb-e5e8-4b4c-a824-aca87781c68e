server:
  port: 8888
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: mcp
  main:
    web-application-type: none
  ai:
    dashscope:
      api-key: ${AI_DASHSCOPE_API_KEY}
    mcp:
      client:
        toolcallback:
          enabled: true
        sse:
          connections:
            server1:
              # 实际的连接地址为：http://localhost:8080/sse/mcp
              url: http://localhost:8080/
  mandatory-file-encoding: UTF-8

# 调试日志
logging:
  level:
    io:
      modelcontextprotocol:
        client: DEBUG
        spec: DEBUG

ai:
  user:
    input: 北京的天气如何？
