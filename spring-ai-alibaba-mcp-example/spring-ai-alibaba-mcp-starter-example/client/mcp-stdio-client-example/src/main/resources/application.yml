spring:
  application:
    name: mcp
  main:
    web-application-type: none
  ai:
    dashscope:
      api-key: ${AI_DASHSCOPE_API_KEY}
    mcp:
      client:
        stdio:
          servers-configuration: classpath:/mcp-servers-config.json
          # 直接配置
          # connections:
          #   server1:
          #     command: java
          #     args:
          #       - -jar
          #       - /Users/<USER>/mcp-stdio-server-exmaple-0.0.1-SNAPSHOT.jar  # 放一个绝对路径，修改为server jar包所在位置
  mandatory-file-encoding: UTF-8

# 调试日志
logging:
  level:
    io:
      modelcontextprotocol:
        client: DEBUG
        spec: DEBUG

server:
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
