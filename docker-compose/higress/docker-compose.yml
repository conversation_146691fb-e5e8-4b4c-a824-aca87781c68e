version: '3.8'

services:

  # Higress AI Cache Use
  redis:
    image: redis
    ports:
      - "6379:6379"
    environment:
      - TZ=Asia/Shanghai
    container_name: spring-ai-alibaba-higress-redis
    volumes:
      # - ./data:/data
      - ./conf/redis.conf:/etc/redis/redis-stand.conf
    command: redis-server /etc/redis/redis-stand.conf
    networks:
        - spring-ai-alibaba-higress

  higress:
    container_name: spring-ai-alibaba-higress
    pull_policy: always
    tty: true
    restart: unless-stopped
    volumes:
      - ./data:/data
    image: higress-registry.cn-hangzhou.cr.aliyuncs.com/higress/all-in-one:latest
    ports:
      - "8001:8001"
      - "8443:8443"
      - "8080:8080"
    networks:
      - spring-ai-alibaba-higress

networks:
  spring-ai-alibaba-higress:
    driver: bridge
