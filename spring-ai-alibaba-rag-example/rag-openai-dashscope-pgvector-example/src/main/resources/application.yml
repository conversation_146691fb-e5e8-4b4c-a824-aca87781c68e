server:
  port: 9000

spring:
  datasource:
    url: *****************************************
    username: postgres
    password: postgres
  application:
    name: rag-openai-dashscope-pgvector-example

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  ai:
    openai:
      api-key: ${AI_DASHSCOPE_API_KEY}
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      chat:
        options:
          model: qwen-plus-latest
      embedding:
        options:
          model: text-embedding-v3
          dimensions: 1024
    vectorstore:
      pgvector:
        table-name: mxy_rag_vector
        initialize-schema: true
        dimensions: 1024
        index-type: hnsw