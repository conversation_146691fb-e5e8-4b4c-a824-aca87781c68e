spring:
  application:
    name: spring-ai-alibaba-rag-milvus-example

  ai:
    dashscope:
      api-key: ${AI_DASHSCOPE_API_KEY}

    vectorstore:
      milvus:
        client:
          host: ${MILVUS_HOST:localhost} # default: localhost
          port: ${MILVUS_PORT:19530} # default: 19530
          username: ${MILVUS_USERNAME:root} # default: root
          password: ${MILVUS_PASSWORD:milvus} # default: milvus
        databaseName: ${MILVUS_DATABASE_NAME:default} # default: default
        collectionName: ${MILVUS_COLLECTION_NAME:vector_store} # default: vector_store
        embeddingDimension: 1536 # default: 1536
        indexType: IVF_FLAT # default: IVF_FLAT
        metricType: COSINE # default: COSINE
