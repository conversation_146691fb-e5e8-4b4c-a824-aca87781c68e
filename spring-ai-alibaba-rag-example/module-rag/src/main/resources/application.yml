# Copyright 2024-2025 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

server:
  port: 10014

spring:
  application:
    name: spring-ai-alibaba-module-rag-example

  datasource:
    url: *********************************************
    username: root
    password: password

  ai:
    dashscope:
      api-key: ${AI_DASHSCOPE_API_KEY}

    vectorstore:
      elasticsearch:
        index-name: spring-ai-alibaba-index
        similarity: cosine
        dimensions: 1536

  elasticsearch:
    uris: http://127.0.0.1:9200
