<?xml version="1.0" encoding="UTF-8"?>

<!--
   Copyright 2024-2026 the original author or authors.

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

        https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.alibaba.cloud.ai</groupId>
		<version>${revision}</version>
		<artifactId>spring-ai-alibaba-rag-example</artifactId>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<version>${revision}</version>
	<artifactId>module-rag</artifactId>
	<name>module-rag</name>
	<description>Module RAG Use Example for Spring AI Alibaba</description>

	<dependencies>
		<dependency>
			<groupId>com.alibaba.cloud.ai</groupId>
			<artifactId>spring-ai-alibaba-starter-dashscope</artifactId>
			<version>${spring-ai-alibaba.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<!-- Fixes : 'co.elastic.clients.elasticsearch._types.KnnSearch$Builder co.elastic.clients.elasticsearch._types.KnnSearch$Builder.k(java.lang.Long)'-->
		<!-- Spring AI Issue:
			1. https://github.com/spring-projects/spring-ai/issues/2220
			2. https://github.com/spring-projects/spring-ai/issues/2270
		-->
		<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-starter-vector-store-elasticsearch</artifactId>
			<exclusions>
				<exclusion>
					<groupId>co.elastic.clients</groupId>
					<artifactId>elasticsearch-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>co.elastic.clients</groupId>
			<artifactId>elasticsearch-java</artifactId>
			<version>8.13.4</version>
			<exclusions>
				<exclusion>
					<artifactId>elasticsearch-rest-client</artifactId>
					<groupId>org.elasticsearch.client</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<artifactId>elasticsearch-rest-client</artifactId>
			<groupId>org.elasticsearch.client</groupId>
			<version>8.13.4</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-advisors-vector-store</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-markdown-document-reader</artifactId>
		</dependency>
	</dependencies>

	<repositories>
		<repository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/milestone</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>${maven-deploy-plugin.version}</version>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>