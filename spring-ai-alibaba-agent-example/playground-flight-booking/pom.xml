<?xml version="1.0" encoding="UTF-8"?>

<!--
   Copyright 2023-2024 the original author or authors.

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

        https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.alibaba.cloud.ai</groupId>
		<artifactId>spring-ai-alibaba-agent-example</artifactId>
		<version>${revision}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>playground-flight-booking</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>playground-flight-booking-example</name>
	<description>Spring AI Alibaba - Flight Booking Demo</description>

	<properties>
		<java.version>17</java.version>
		<vaadin.version>24.4.7</vaadin.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.alibaba.cloud.ai</groupId>
			<artifactId>spring-ai-alibaba-starter-dashscope</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

		<!-- Other spring dependencies -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-advisors-vector-store</artifactId>
		</dependency>
	</dependencies>

	<build>
		<defaultGoal>spring-boot:run</defaultGoal>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<jvmArguments>-Xdebug
						-Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5247
					</jvmArguments>
					<wait>500</wait>
					<maxAttempts>240</maxAttempts>
					<executable>true</executable>
					<layout>JAR</layout>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
						<configuration>
							<attach>false</attach>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>${maven-deploy-plugin.version}</version>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>17</source>
					<target>17</target>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<repositories>
		<repository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/milestone</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>

	<profiles>
		<profile>
			<id>build-frontend</id>
			<build>
				<plugins>
					<plugin>
						<artifactId>maven-clean-plugin</artifactId>
						<version>3.2.0</version>
						<configuration>
							<filesets>
								<fileset>
									<directory>${basedir}/src/main/resources/static/assets</directory>
									<includes>
										<include>*.js</include>
										<include>*.css</include>
									</includes>
								</fileset>
							</filesets>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>exec-maven-plugin</artifactId>
						<version>1.6.0</version>
						<executions>
							<execution>
								<id>exec-npm-run-build</id>
								<phase>compile</phase>
								<goals>
									<goal>exec</goal>
								</goals>
								<configuration>
									<executable>npm</executable>
									<arguments>
										<argument>run</argument>
										<argument>build</argument>
									</arguments>
									<workingDirectory>${basedir}/frontend</workingDirectory>
								</configuration>
							</execution>
						</executions>
					</plugin>

					<plugin>
						<artifactId>maven-resources-plugin</artifactId>
						<executions>
							<execution>
								<id>copy-resource-index</id>
								<phase>compile</phase>
								<goals>
									<goal>copy-resources</goal>
								</goals>
								<configuration>
									<outputDirectory>src/main/resources/templates</outputDirectory>
									<resources>
										<resource>
											<directory>${basedir}/frontend/dist</directory>
											<includes>
												<include>index.html</include>
											</includes>
										</resource>
									</resources>
								</configuration>
							</execution>
							<execution>
								<id>copy-resource-static</id>
								<phase>compile</phase>
								<goals>
									<goal>copy-resources</goal>
								</goals>
								<configuration>
									<outputDirectory>src/main/resources/static</outputDirectory>
									<resources>
										<resource>
											<directory>${basedir}/frontend/dist</directory>
											<excludes>
												<exclude>index.html</exclude>
											</excludes>
										</resource>
									</resources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

</project>
