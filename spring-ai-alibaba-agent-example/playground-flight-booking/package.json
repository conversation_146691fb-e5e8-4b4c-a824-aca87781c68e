{"name": "no-name", "license": "UNLICENSED", "type": "module", "dependencies": {"@polymer/polymer": "3.5.1", "@vaadin/bundles": "24.4.4", "@vaadin/common-frontend": "0.0.19", "@vaadin/hilla-file-router": "24.4.4", "@vaadin/hilla-frontend": "24.4.4", "@vaadin/hilla-lit-form": "24.4.4", "@vaadin/hilla-react-auth": "24.4.4", "@vaadin/hilla-react-crud": "24.4.4", "@vaadin/hilla-react-form": "24.4.4", "@vaadin/hilla-react-i18n": "24.4.4", "@vaadin/hilla-react-signals": "24.4.4", "@vaadin/polymer-legacy-adapter": "24.4.4", "@vaadin/react-components": "24.4.4", "@vaadin/router": "1.7.5", "@vaadin/vaadin-development-mode-detector": "2.0.7", "@vaadin/vaadin-lumo-styles": "24.4.4", "@vaadin/vaadin-material-styles": "24.4.4", "@vaadin/vaadin-themable-mixin": "24.4.4", "@vaadin/vaadin-usage-statistics": "2.1.2", "construct-style-sheets-polyfill": "3.1.0", "date-fns": "2.29.3", "lit": "3.1.4", "nanoid": "^5.0.6", "react": "18.3.1", "react-dom": "18.3.1", "react-markdown": "^9.0.1", "react-router-dom": "6.23.1"}, "devDependencies": {"@babel/preset-react": "7.24.7", "@rollup/plugin-replace": "5.0.7", "@rollup/pluginutils": "5.1.0", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@vaadin/hilla-generator-cli": "24.4.4", "@vaadin/hilla-generator-core": "24.4.4", "@vaadin/hilla-generator-plugin-backbone": "24.4.4", "@vaadin/hilla-generator-plugin-barrel": "24.4.4", "@vaadin/hilla-generator-plugin-client": "24.4.4", "@vaadin/hilla-generator-plugin-model": "24.4.4", "@vaadin/hilla-generator-plugin-push": "24.4.4", "@vaadin/hilla-generator-plugin-subtypes": "24.4.4", "@vaadin/hilla-generator-utils": "24.4.4", "@vitejs/plugin-react": "4.3.1", "async": "3.2.5", "glob": "10.4.1", "rollup-plugin-brotli": "3.1.0", "rollup-plugin-visualizer": "5.12.0", "strip-css-comments": "5.0.0", "transform-ast": "2.4.4", "typescript": "5.4.5", "vite": "5.3.3", "vite-plugin-checker": "0.6.4", "workbox-build": "7.1.1", "workbox-core": "7.1.0", "workbox-precaching": "7.1.0"}, "vaadin": {"dependencies": {"@polymer/polymer": "3.5.1", "@vaadin/bundles": "24.4.4", "@vaadin/common-frontend": "0.0.19", "@vaadin/hilla-file-router": "24.4.4", "@vaadin/hilla-frontend": "24.4.4", "@vaadin/hilla-lit-form": "24.4.4", "@vaadin/hilla-react-auth": "24.4.4", "@vaadin/hilla-react-crud": "24.4.4", "@vaadin/hilla-react-form": "24.4.4", "@vaadin/hilla-react-i18n": "24.4.4", "@vaadin/hilla-react-signals": "24.4.4", "@vaadin/polymer-legacy-adapter": "24.4.4", "@vaadin/react-components": "24.4.4", "@vaadin/router": "1.7.5", "@vaadin/vaadin-development-mode-detector": "2.0.7", "@vaadin/vaadin-lumo-styles": "24.4.4", "@vaadin/vaadin-material-styles": "24.4.4", "@vaadin/vaadin-themable-mixin": "24.4.4", "@vaadin/vaadin-usage-statistics": "2.1.2", "construct-style-sheets-polyfill": "3.1.0", "date-fns": "2.29.3", "lit": "3.1.4", "react": "18.3.1", "react-dom": "18.3.1", "react-router-dom": "6.23.1"}, "devDependencies": {"@babel/preset-react": "7.24.7", "@rollup/plugin-replace": "5.0.7", "@rollup/pluginutils": "5.1.0", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@vaadin/hilla-generator-cli": "24.4.4", "@vaadin/hilla-generator-core": "24.4.4", "@vaadin/hilla-generator-plugin-backbone": "24.4.4", "@vaadin/hilla-generator-plugin-barrel": "24.4.4", "@vaadin/hilla-generator-plugin-client": "24.4.4", "@vaadin/hilla-generator-plugin-model": "24.4.4", "@vaadin/hilla-generator-plugin-push": "24.4.4", "@vaadin/hilla-generator-plugin-subtypes": "24.4.4", "@vaadin/hilla-generator-utils": "24.4.4", "@vitejs/plugin-react": "4.3.1", "async": "3.2.5", "glob": "10.4.1", "rollup-plugin-brotli": "3.1.0", "rollup-plugin-visualizer": "5.12.0", "strip-css-comments": "5.0.0", "transform-ast": "2.4.4", "typescript": "5.4.5", "vite": "5.3.3", "vite-plugin-checker": "0.6.4", "workbox-build": "7.1.1", "workbox-core": "7.1.0", "workbox-precaching": "7.1.0"}, "hash": "dc4e9712972308c9c0823eb883252b0b3fd418f39067b89c489a2f64f7136ec1"}, "overrides": {"@vaadin/bundles": "$@vaadin/bundles", "@vaadin/common-frontend": "$@vaadin/common-frontend", "construct-style-sheets-polyfill": "$construct-style-sheets-polyfill", "lit": "$lit", "@vaadin/router": "$@vaadin/router", "@polymer/polymer": "$@polymer/polymer", "nanoid": "$nanoid", "react-markdown": "$react-markdown", "@vaadin/polymer-legacy-adapter": "$@vaadin/polymer-legacy-adapter", "@vaadin/vaadin-development-mode-detector": "$@vaadin/vaadin-development-mode-detector", "@vaadin/vaadin-usage-statistics": "$@vaadin/vaadin-usage-statistics", "@vaadin/react-components": "$@vaadin/react-components", "react-dom": "$react-dom", "@vaadin/hilla-frontend": "$@vaadin/hilla-frontend", "@vaadin/hilla-react-auth": "$@vaadin/hilla-react-auth", "react": "$react", "@vaadin/hilla-react-crud": "$@vaadin/hilla-react-crud", "@vaadin/hilla-file-router": "$@vaadin/hilla-file-router", "react-router-dom": "$react-router-dom", "@vaadin/hilla-react-i18n": "$@vaadin/hilla-react-i18n", "@vaadin/hilla-lit-form": "$@vaadin/hilla-lit-form", "@vaadin/hilla-react-form": "$@vaadin/hilla-react-form", "@vaadin/hilla-react-signals": "$@vaadin/hilla-react-signals", "date-fns": "$date-fns", "@vaadin/vaadin-themable-mixin": "$@vaadin/vaadin-themable-mixin", "@vaadin/vaadin-lumo-styles": "$@vaadin/vaadin-lumo-styles", "@vaadin/vaadin-material-styles": "$@vaadin/vaadin-material-styles"}}