{"name": "ui-vue3", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "check:i18n": "node --loader ts-node/esm src/base/i18n/sortI18n.ts", "preview": "vite preview", "test:unit": "vitest", "build": "yarn && prettier --write src/ && vite build", "format": "prettier --write src/", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "lint:fix": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "prepare": "husky || true", "prettier-format": "prettier --write src/", "prettier-check": "prettier --check src/"}, "resolutions": {"jackspeak": "2.1.1"}, "dependencies": {"@antv/g2": "^5.1.12", "@iconify/json": "^2.2.157", "@iconify/vue": "^4.1.1", "@types/lodash": "^4.14.202", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "ant-design-vue": "4.x", "dayjs": "^1.11.13", "less": "^4.2.0", "lodash": "^4.17.21", "mockjs": "^1.1.0", "monaco-editor": "^0.45.0", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinyin-pro": "^3.19.3", "ts-node": "^10.9.2", "tslib": "^2.6.2", "vue": "^3.3.10", "vue-clipboard3": "^2.0.0", "vue-i18n": "^9.8.0", "vue-router": "^4.2.5", "vue3-colorpicker": "^2.2.3", "vue3-markdown-it": "^1.0.10"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/jsdom": "^21.1.6", "@types/mockjs": "^1.0.10", "@types/node": "^20.10.6", "@vitejs/plugin-vue": "^4.5.1", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.4.0", "cypress": "^13.6.1", "eslint": "^8.49.0", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-vue": "^9.17.0", "husky": "^9.0.6", "jsdom": "^23.0.1", "npm-run-all2": "^6.1.1", "prettier": "^3.0.3", "start-server-and-test": "^2.0.3", "typescript": "~5.2.0", "vite": "^5.0.5", "vitest": "^1.0.1", "vue-tsc": "^1.8.25"}}