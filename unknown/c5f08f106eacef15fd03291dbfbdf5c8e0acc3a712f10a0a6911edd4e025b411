#
# Copyright 2024-2025 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

name: Linter

on:
  push:
    branches:
      - main
    paths-ignore:
      - '**.java'
  pull_request:
    branches:
      - main
    paths-ignore:
      - '**.java'

permissions:
  contents: read

jobs:
  linter:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2
      - uses: ./tools/github-actions/setup-deps
      - run: make tools
      # - run: make lint
      # file newline end check
      - run: make newline-check
