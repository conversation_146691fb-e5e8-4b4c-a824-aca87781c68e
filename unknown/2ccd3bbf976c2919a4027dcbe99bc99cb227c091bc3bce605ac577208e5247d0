# Spring AI Alibaba Examples

> Spring AI Alibaba Repo: https://github.com/alibaba/spring-ai-alibaba
>
> Spring AI Alibaba Website: https://java2ai.com
>
> Spring AI Alibaba Website Repo: https://github.com/springaialibaba/spring-ai-alibaba-website

English | [中文](./README.md)

## Introduction

This repository contains various Example module projects that demonstrate the usage of Spring AI and Spring AI Alibaba, from basic to advanced practices and best practices for AI projects. For more detailed introductions, please refer to the README.md of each subproject and the Spring AI Alibaba website.

## How to Contribute

We welcome contributions in any form, including but not limited to:

1. Usage examples of Spring AI and Spring AI Alibaba;
2. Use of Spring AI and Spring AI Alibaba APIs;
3. Best practices for AI projects, etc.

The project has been initially set up, and we will add and remove certain Example modules based on the main repository. Please follow Spring AI Alibaba for more information.

