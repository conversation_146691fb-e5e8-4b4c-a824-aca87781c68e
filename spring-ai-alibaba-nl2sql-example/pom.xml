<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>spring-ai-alibaba-nl2sql-example</artifactId>
    <modules>
        <module>vector-management</module>
        <module>mcp</module>
        <module>chat</module>
    </modules>

    <parent>
        <groupId>com.alibaba.cloud.ai</groupId>
        <artifactId>spring-ai-alibaba-examples</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <description>Spring AI Alibaba Nl2SQl Example</description>
    <name>Spring AI Alibaba Nl2SQl Examples</name>


    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-ai-alibaba.version>*******-SNAPSHOT</spring-ai-alibaba.version>
    </properties>

</project>