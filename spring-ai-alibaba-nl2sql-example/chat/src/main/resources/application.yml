server:
  port: 8065 # 服务器端口配置
spring:
  thymeleaf:
    enabled: true
    check-template-location: true
    prefix: classpath:/static/
    mvc:
      view:
        prefix: /static/
        suffix: .html
      static-path-pattern: /static/**
    resources:
      static-locations: classpath:/static/
  web:
    resources:
      static-locations: classpath:/static/
  ai:
    dashscope:
      api-key: ${AI_DASHSCOPE_API_KEY}
    mcp:
      server:
        name: xiyan-server    # MCP服务器名称
        version: 0.0.1           # 服务器版本号
    vectorstore:
      analytic:
        enabled: false
        collectName: chatBi
        regionId: cn-hangzhou
        dbInstanceId:
        managerAccount:
        managerAccountPassword:
        namespace: chat
        namespacePassword: chat
        defaultTopK: 6
        defaultSimilarityThreshold: 0.75
        accessKeyId:
        accessKeySecret:
    openai:
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      api-key: ${AI_DASHSCOPE_API_KEY}
      model: qwen-max
chatBi:
  dbConfig:
    url:
    username:
    password:
    schema:
    connection-type: jdbc
    dialect-type: mysql
rest:
  connect:
    timeout: 600 # 连接超时时间（秒）
  read:
    timeout: 600 # 读取超时时间（秒）
webclient:
  response:
    timeout: 600 # 响应超时时间（秒）
