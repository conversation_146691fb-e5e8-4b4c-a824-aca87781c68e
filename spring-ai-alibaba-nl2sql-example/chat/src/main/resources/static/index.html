<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBI 智能查询系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.8rem;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .content {
            padding: 50px;
        }

        .query-section {
            margin-bottom: 40px;
        }

        .query-section h2 {
            color: #333;
            margin-bottom: 25px;
            font-size: 1.6rem;
            font-weight: 600;
        }

        .input-group {
            margin-bottom: 25px;
        }

        .input-group textarea {
            width: 100%;
            padding: 24px;
            border: 2px solid #e1e8ed;
            border-radius: 18px;
            font-size: 18px;
            resize: vertical;
            min-height: 200px;
            transition: all 0.3s ease;
            font-family: inherit;
            line-height: 1.7;
        }

        .input-group textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .input-group textarea::placeholder {
            color: #999;
            font-style: italic;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 15px;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
        }

        .btn-primary:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .result-section {
            border-top: 2px solid #f0f0f0;
            padding-top: 40px;
        }

        .result-section h3 {
            color: #333;
            margin-bottom: 25px;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .result-display {
            min-height: 180px;
            border: 2px dashed #e1e8ed;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .result-display.loading {
            border-color: #4facfe;
            background: linear-gradient(135deg, #f8fcff 0%, #f0f9ff 100%);
        }

        .result-display.success {
            border-color: #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #f0fff4 100%);
            border-style: solid;
        }

        .result-display.error {
            border-color: #dc3545;
            background: linear-gradient(135deg, #fff8f8 0%, #fff0f0 100%);
            border-style: solid;
        }

        .loading-spinner {
            font-size: 2.5rem;
            color: #4facfe;
            margin-bottom: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-text {
            background: white;
            padding: 25px;
            border-radius: 12px;
            border-left: 5px solid #28a745;
            text-align: left;
            font-size: 16px;
            line-height: 1.7;
            color: #333;
            white-space: pre-wrap;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .error-text {
            color: #dc3545;
            font-size: 16px;
            font-weight: 500;
        }

        .empty-text {
            color: #6c757d;
            font-size: 16px;
            line-height: 1.6;
        }

        .timestamp {
            margin-top: 20px;
            font-size: 13px;
            color: #6c757d;
            text-align: right;
            font-style: italic;
        }

        .tips {
            background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
            border: 1px solid #ffeaa7;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            color: #856404;
        }

        .tips h4 {
            margin-bottom: 10px;
            color: #856404;
            font-size: 14px;
            font-weight: 600;
        }

        .tips p {
            font-size: 13px;
            line-height: 1.5;
            margin: 0;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2.2rem;
            }

            .content {
                padding: 30px 20px;
            }

            .input-group textarea {
                min-height: 120px;
                padding: 15px;
            }

            .btn-primary {
                padding: 15px 25px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>🤖 ChatBI 智能查询</h1>
        <p>基于AI的智能数据分析助手</p>
    </div>

    <div class="content">
        <div class="tips">
            <h4>💡 使用提示</h4>
            <p>支持自然语言查询，您可以用日常语言描述您想了解的数据信息，系统会智能分析并返回结果。支持 Ctrl+Enter 快捷提交。</p>
        </div>

        <div class="query-section">
            <h2>📝 输入您的查询</h2>
            <div class="input-group">
                <label style="font-weight:600;margin-bottom:8px;display:block;">选择向量库类型：</label>
                <select id="vectorTypeSelect" style="margin-bottom:15px;padding:8px 12px;border-radius:8px;border:1px solid #e1e8ed;font-size:15px;">
                    <option value="memory">Memory(SimpleVector)</option>
                    <option value="analyticDb">AnalyticDB</option>
                </select>
                <textarea
                        id="queryInput"
                        placeholder="请输入您想查询的问题，例如：
                        • 销售额最高的产品是什么？
                        • 上个月的用户增长情况如何？
                        • 哪个地区的订单量最多？
                        • 客户满意度趋势分析..."
                ></textarea>
            </div>
            <button id="queryBtn" class="btn-primary">
                🚀 开始查询
            </button>
        </div>

        <div class="result-section">
            <h3>📊 查询结果</h3>
            <div id="resultDisplay" class="result-display">
                <div class="empty-text">
                    🔍 等待查询中...
                    <br><br>请在上方输入您的问题并点击查询按钮
                    <br>系统将为您智能分析并返回结果
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const queryInput = document.getElementById('queryInput');
    const queryBtn = document.getElementById('queryBtn');
    const resultDisplay = document.getElementById('resultDisplay');

    // 查询函数
    async function performQuery(input) {
        const vectorType = document.getElementById('vectorTypeSelect').value;
        if (!input.trim()) {
            alert('请输入查询内容');
            return;
        }

        // 设置加载状态
        setLoading(true);

        try {
            const apiUrl = vectorType === 'analyticDb' ? 'http://localhost:8065/chat' : 'http://localhost:8065/simpleChat';
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ input: input.trim() })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.text();
            showResult(result);
        } catch (error) {
            showError(error.message || '查询失败，请检查网络连接或接口状态');
        } finally {
            setLoading(false);
        }
    }

    // 设置加载状态
    function setLoading(loading) {
        queryBtn.disabled = loading;

        if (loading) {
            queryBtn.innerHTML = '🔄 查询中...';
            resultDisplay.className = 'result-display loading';
            resultDisplay.innerHTML = `
                    <div class="loading-spinner">🔄</div>
                    <div style="font-size: 18px; font-weight: 500; margin-bottom: 10px;">AI正在分析您的问题...</div>
                    <div style="font-size: 14px; color: #6c757d;">
                        请稍候，系统正在智能解析并查询相关数据
                    </div>
                `;
        } else {
            queryBtn.innerHTML = '🚀 开始查询';
        }
    }

    // 显示成功结果
    function showResult(result) {
        resultDisplay.className = 'result-display success';
        resultDisplay.innerHTML = `
                <div class="result-text">${result}</div>
                <div class="timestamp">
                    📅 查询完成时间：${new Date().toLocaleString('zh-CN')}
                </div>
            `;
    }

    // 显示错误
    function showError(error) {
        resultDisplay.className = 'result-display error';
        resultDisplay.innerHTML = `
                <div class="error-text">
                    ❌ 查询失败<br><br>
                    ${error}
                </div>
                <div style="margin-top: 20px; font-size: 14px; color: #6c757d;">
                    <strong>💡 可能的解决方案：</strong><br>
                    • 检查网络连接是否正常<br>
                    • 确认ChatBI服务是否正在运行<br>
                    • 验证接口地址是否正确<br>
                    • 尝试重新提交查询
                </div>
            `;
    }

    // 绑定查询按钮事件
    queryBtn.addEventListener('click', () => {
        performQuery(queryInput.value);
    });

    // 绑定回车键事件
    queryInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && e.ctrlKey) {
            performQuery(queryInput.value);
        }
    });

    // 自动聚焦到输入框
    queryInput.focus();
</script>
</body>
</html>
