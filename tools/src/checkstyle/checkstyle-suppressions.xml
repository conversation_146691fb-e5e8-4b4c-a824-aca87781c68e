<?xml version="1.0"?>
<!--
  ~ Copyright 2023-2024 the original author or authors.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE suppressions PUBLIC
		"-//Checkstyle//DTD SuppressionFilter Configuration 1.2//EN"
		"https://checkstyle.org/dtds/suppressions_1_2.dtd">
<suppressions>
	<suppress files="[\\/]src[\\/]test[\\/]java[\\/]" checks="Javadoc*" />
	<suppress files=".*Tests\.java" checks="Javadoc*" />
	<suppress files="generated-sources" checks="[a-zA-Z0-9]*" />
	<suppress files="org[\\/]eclipse[\\/]jdt[\\/]internal[\\/]formatter[\\/]linewrap[\\/]WrapPreparator\.java" checks="[a-zA-Z0-9]*" />

	<!-- A list of class files that need to be suppressed -->
	<suppress files="NacosMcpRegistryProperties.java" checks="FinalClass"/>

</suppressions>
