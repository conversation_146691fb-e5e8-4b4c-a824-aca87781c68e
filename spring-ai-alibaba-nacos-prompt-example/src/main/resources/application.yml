# Copyright 2024-2025 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

server:
  port: 10010

spring:
  application:
    name: spring-ai-alibaba-nacos-prompt-example

  # 指定监听的 prompt 配置
  config:
    import:
      - "optional:nacos:prompt-config.json"
  nacos:
    username: nacos
    password: nacos

  ai:
    # 开启 nacos 的 prompt tmpl 监听功能
    nacos:
      prompt:
        template:
          enabled: true
