server:
  port: 8080

spring:
  ai:
    dashscope:
      api-key: ${AI_DASHSCOPE_API_KEY}
    memory:
      redis:
        host: localhost
        port: 6379
        password:
        timeout:  5000
    chat:
      memory:
        repository:
          jdbc:
            mysql:
              jdbc-url: *********************************************************************************************************************************************************************************************************************************************************************************************************
              username: root
              password: root
              driver-class-name: com.mysql.cj.jdbc.Driver
              enabled: true

