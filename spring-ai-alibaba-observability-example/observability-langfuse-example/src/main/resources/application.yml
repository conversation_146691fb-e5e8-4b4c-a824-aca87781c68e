spring:
  application:
    name: observability-models-dashscope

  ai:
    dashscope:
      api-key: ${AI_DASHSCOPE_API_KEY}

    # spring ai alibaba weather tool calling config
    alibaba:
      toolcalling:
        weather:
          api-key: ${WEATHER_API_KEY}
          enabled: true

    # Chat config items
    chat:
      client:
        observations:
          # default value is false.
          log-prompt: true
      observations:
        log-prompt: true
        log-completion: true
        include-error-logging: true

    # vector store config items
    vectorstore:
      observations:
        log-query-response: true

    # tools config items
    tools:
      observations:
        # default value is false.
        include-content: true

    image:
      observations:
        log-prompt: true

  http:
    client:
      read-timeout: 60s

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      # health status check with detailed messages
      show-details: always
  tracing:
    sampling:
      # trace information with every request
      probability: 1.0
  observations:
    annotations:
      enabled: true

otel:
  service:
    name: spring-ai-alibaba-graph-langfuse
  resource:
    attributes:
      deployment.environment: development
  # configure exporter
  traces:
    exporter: otlp
    sampler: always_on
  metrics:
    exporter: otlp
  # logs exportation inhibited for langfuse currently cannot support
  logs:
    exporter: none
  exporter:
    otlp:
      endpoint: "https://cloud.langfuse.com/api/public/otel"
      headers:
        Authorization: "Basic ${YOUR_BASE64_ENCODED_CREDENTIALS}"
      protocol: http/protobuf