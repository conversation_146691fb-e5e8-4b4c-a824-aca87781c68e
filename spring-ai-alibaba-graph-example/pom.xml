<?xml version="1.0" encoding="UTF-8"?>

<!--
   Copyright 2023-2024 the original author or authors.

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

        https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.alibaba.cloud.ai</groupId>
        <artifactId>spring-ai-alibaba-examples</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <version>${revision}</version>
    <artifactId>spring-ai-alibaba-graph-example</artifactId>
    <packaging>pom</packaging>

    <description>Spring AI Alibaba Graph Example</description>
    <name>Spring AI Alibaba Graph Examples</name>

    <modules>
        <module>workflow-review-classifier</module>
        <module>workflow-writing-assistant</module>
        <module>multiagent-openmanus</module>
        <module>stream-node</module>
        <module>mcp-node</module>
        <module>human-node</module>
        <module>usecase-field-classifier</module>
        <module>product-analysis-graph</module>
        <module>parallel-node</module>
        <module>graph-observability-langfuse</module>
        <module>big-tool</module>
        <module>react</module>
        <module>reflection</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven-deploy-plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
