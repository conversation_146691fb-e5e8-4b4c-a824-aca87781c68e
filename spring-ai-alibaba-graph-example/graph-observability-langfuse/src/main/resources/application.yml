server:
  port: 8080
spring:
  application:
    name: graph-observation-langfuse
  ai:
    dashscope:
      api-key: ${AI_DASHSCOPE_API_KEY}
      chat:
        options:
          model: qwen-max
    alibaba:
      graph:
        observation:
          enabled: true


management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      # health status check with detailed messages
      show-details: always
  tracing:
    sampling:
      # trace information with every request
      probability: 1.0
  observations:
    annotations:
      enabled: true

otel:
  service:
    name: spring-ai-alibaba-graph-langfuse
  resource:
    attributes:
      deployment.environment: development
  # configure exporter
  traces:
    exporter: otlp
    sampler: always_on
  metrics:
    exporter: otlp
  # logs exportation inhibited for langfuse currently cannot support
  logs:
    exporter: none
  exporter:
    otlp:
      endpoint: "https://cloud.langfuse.com/api/public/otel"
      headers:
        Authorization: "Basic ${YOUR_BASE64_ENCODED_CREDENTIALS}"
      protocol: http/protobuf